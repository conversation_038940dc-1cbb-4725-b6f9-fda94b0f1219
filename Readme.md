# Prerequisites
## XCode
I needed to explicitly install `MetalToolchain` having installed `XCode` from the App Store:
```shell
$ xcodebuild -downloadComponent MetalToolchain
$ sudo xcodebuild -license
$ xcrun -sdk macosx metal
$ xcrun -find metallib
```

Then there was `pixi`:
```shell
$ curl -fsSL https://raw.githubusercontent.com/modularml/pixi/main/install.sh | sh
$ pixi init
$ pixi add "mojo<1.0.0"
$ pixi shell
```
